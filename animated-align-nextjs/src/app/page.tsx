'use client';

import { useState } from 'react';
import AnimatedAlignPositioned, { Alignment, Touch } from '@/components/AnimatedAlignPositioned';

export default function Home() {
  const [dx, setDx] = useState(0);
  const [dy, setDy] = useState(0);
  const [alignment, setAlignment] = useState(Alignment.CENTER);
  const [touch, setTouch] = useState(Touch.INSIDE);
  const [rotateDegrees, setRotateDegrees] = useState(0);
  const [textScale, setTextScale] = useState(1);
  const [fontSize, setFontSize] = useState(16);
  const [customText, setCustomText] = useState('Animated Text');
  const [manualDx, setManualDx] = useState('0');
  const [manualDy, setManualDy] = useState('0');

  // Reset function
  const handleReset = () => {
    setDx(0);
    setDy(0);
    setTouch(Touch.INSIDE);
    setRotateDegrees(0);
    setTextScale(1);
    setFontSize(16);
    setCustomText('Animated Text');
    setManualDx('0');
    setManualDy('0');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          AnimatedAlignPositioned Demo
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Controls Panel */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">Controls</h2>

            <div className="space-y-4">
              {/* Text Controls */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Custom Text
                </label>
                <input
                  type="text"
                  value={customText}
                  onChange={(e) => setCustomText(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Text Scale: {textScale.toFixed(2)}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="3"
                  step="0.1"
                  value={textScale}
                  onChange={(e) => setTextScale(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Font Size: {fontSize}px
                </label>
                <input
                  type="range"
                  min="12"
                  max="48"
                  value={fontSize}
                  onChange={(e) => setFontSize(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-2">
                    Manual DX
                  </label>
                  <input
                    type="number"
                    value={manualDx}
                    onChange={(e) => {
                      setManualDx(e.target.value);
                      setDx(Number(e.target.value));
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-2">
                    Manual DY
                  </label>
                  <input
                    type="number"
                    value={manualDy}
                    onChange={(e) => {
                      setManualDy(e.target.value);
                      setDy(Number(e.target.value));
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* DX Control */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  DX (Horizontal Offset): {dx}{Math.abs(dx) <= 1 ? ' (relative)' : 'px (absolute)'}
                </label>
                <input
                  type="range"
                  min="-1"
                  max="1"
                  step="0.001"
                  value={dx}
                  onChange={(e) => {
                    setDx(Number(e.target.value));
                    setManualDx(e.target.value);
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* DY Control */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  DY (Vertical Offset): {dy}{Math.abs(dy) <= 1 ? ' (relative)' : 'px (absolute)'}
                </label>
                <input
                  type="range"
                  min="-1"
                  max="1"
                  step="0.001"
                  value={dy}
                  onChange={(e) => {
                    setDy(Number(e.target.value));
                    setManualDy(e.target.value);
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Rotation Control */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Rotation: {rotateDegrees}°
                </label>
                <input
                  type="range"
                  min="0"
                  max="360"
                  value={rotateDegrees}
                  onChange={(e) => setRotateDegrees(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Alignment Control
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Alignment
                </label>
                <select
                  value={alignment}
                  onChange={(e) => setAlignment(e.target.value as Alignment)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={Alignment.TOP_LEFT}>Top Left</option>
                  <option value={Alignment.TOP_CENTER}>Top Center</option>
                  <option value={Alignment.TOP_RIGHT}>Top Right</option>
                  <option value={Alignment.CENTER_LEFT}>Center Left</option>
                  <option value={Alignment.CENTER}>Center</option>
                  <option value={Alignment.CENTER_RIGHT}>Center Right</option>
                  <option value={Alignment.BOTTOM_LEFT}>Bottom Left</option>
                  <option value={Alignment.BOTTOM_CENTER}>Bottom Center</option>
                  <option value={Alignment.BOTTOM_RIGHT}>Bottom Right</option>
                </select>
              </div> */}

              {/* Touch Control */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Touch Mode
                </label>
                <select
                  value={touch}
                  onChange={(e) => setTouch(e.target.value as Touch)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={Touch.INSIDE}>Inside</option>
                  <option value={Touch.OUTSIDE}>Outside</option>
                  <option value={Touch.MIDDLE}>Middle</option>
                </select>
              </div>

              {/* Reset Button */}
              <div className="flex gap-2">
                <button
                  onClick={handleReset}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  Reset All
                </button>
              </div>
            </div>
          </div>

          {/* Demo Container */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">Preview</h2>

            <div className="relative border-2 border-dashed border-gray-300 bg-gray-50" style={{ height: '812px', width: '375px' }}>
              <AnimatedAlignPositioned
                dx={dx}
                dy={dy}
                alignment={alignment}
                touch={touch}
                rotateDegrees={rotateDegrees}
                duration={0.5}
                curve="easeInOut"
              >
                <div 
                  className="text-blue-500 font-semibold"
                  style={{ 
                    transform: `scale(${textScale})`,
                    fontSize: `${fontSize}px`,
                  }}
                >
                  {customText}
                </div>
              </AnimatedAlignPositioned>
            </div>

            <div className="mt-4 text-sm text-gray-600">
              <p><strong>Container:</strong> The dashed border represents the container bounds</p>
              <p><strong>Text:</strong> The text can be positioned using dx/dy offsets</p>
              <p><strong>Positioning:</strong> Values between -1 and 1 are relative (percentages), others are absolute pixels</p>
            </div>
          </div>
        </div>

        {/* Usage Examples */}
        <div className="mt-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Usage Examples</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Example 1 - Your Mobile Position */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Your Mobile Position</h3>
              <div className="relative bg-gray-100 h-32 border border-gray-300">
                <AnimatedAlignPositioned
                  dx={0.06577775065104166}
                  dy={0.07307060128949545}
                  alignment={Alignment.TOP_LEFT}
                >
                  <div className="bg-red-500 text-white p-2 rounded text-xs">
                    6.58% right, 7.31% down
                  </div>
                </AnimatedAlignPositioned>
              </div>
            </div>

            {/* Example 2 - Relative Center */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Relative Center</h3>
              <div className="relative bg-gray-100 h-32 border border-gray-300">
                <AnimatedAlignPositioned
                  dx={0.1}
                  dy={0.2}
                  alignment={Alignment.CENTER}
                >
                  <div className="bg-green-500 text-white p-2 rounded text-xs">
                    10% right, 20% down
                  </div>
                </AnimatedAlignPositioned>
              </div>
            </div>

            {/* Example 3 - Relative Bottom */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Relative Bottom</h3>
              <div className="relative bg-gray-100 h-32 border border-gray-300">
                <AnimatedAlignPositioned
                  dx={-0.05}
                  dy={-0.1}
                  alignment={Alignment.BOTTOM_RIGHT}
                >
                  <div className="bg-purple-500 text-white p-2 rounded text-xs">
                    -5% left, -10% up
                  </div>
                </AnimatedAlignPositioned>
              </div>
            </div>
          </div>
        </div>

        {/* Code Example */}
        <div className="mt-8 bg-gray-900 text-gray-100 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Code Example</h2>
          <pre className="text-sm overflow-x-auto">
            <code>{`import AnimatedAlignPositioned, { Alignment } from '@/components/AnimatedAlignPositioned';

<AnimatedAlignPositioned
  dx={${dx}}
  dy={${dy}}
  alignment={Alignment.${alignment.toUpperCase()}}
  rotateDegrees={${rotateDegrees}}
  duration={0.5}
  curve="easeInOut"
>
  <div className="bg-blue-500 text-white p-4 rounded-lg">
    Your content here
  </div>
</AnimatedAlignPositioned>`}</code>
          </pre>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/animchain"
            className="inline-block bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            View AnimChain Demo →
          </a>
        </div>
      </div>
    </div>
  );
}
