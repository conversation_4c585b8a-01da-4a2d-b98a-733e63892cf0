'use client';

import { createAnim<PERSON>hain } from '@/components/AnimChain';
import AnimatedAlignPositioned, { Alignment } from '@/components/AnimatedAlignPositioned';

export default function AnimChainDemo() {
  // Create an animation chain similar to the Flutter example
  const animationChain = createAnimChain()
    .repeat(true)
    .initialDelay(150)
    // Show the yellow box and wait 5 seconds
    .next(
      <AnimatedAlignPositioned
        alignment={Alignment.BOTTOM_CENTER}
        rotateDegrees={0}
        duration={0.3}
      >
        <div className="w-16 h-16 bg-yellow-500 rounded-lg shadow-lg flex items-center justify-center text-white font-bold">
          1
        </div>
      </AnimatedAlignPositioned>,
      5000
    )
    // Rotate to the red box in 3 seconds
    .next(
      <AnimatedAlignPositioned
        alignment={Alignment.BOTTOM_CENTER}
        rotateDegrees={180}
        duration={3}
      >
        <div className="w-16 h-16 bg-red-500 rounded-lg shadow-lg flex items-center justify-center text-white font-bold">
          2
        </div>
      </AnimatedAlignPositioned>,
      3000
    )
    // Finally, translate the blue box in the vertical axis
    .next(
      <AnimatedAlignPositioned
        alignment={Alignment.BOTTOM_CENTER}
        dy={-150}
        rotateDegrees={180}
        duration={2}
      >
        <div className="w-16 h-16 bg-blue-500 rounded-lg shadow-lg flex items-center justify-center text-white font-bold">
          3
        </div>
      </AnimatedAlignPositioned>,
      2000
    )
    .build({ className: 'w-full h-full' });

  // Simple sequence example
  const simpleChain = createAnimChain()
    .repeat(true)
    .next(
      <div className="bg-green-500 text-white p-4 rounded-lg text-center">
        Step 1: Green Box
      </div>,
      2000
    )
    .next(
      <div className="bg-purple-500 text-white p-4 rounded-lg text-center">
        Step 2: Purple Box
      </div>,
      2000
    )
    .next(
      <div className="bg-orange-500 text-white p-4 rounded-lg text-center">
        Step 3: Orange Box
      </div>,
      2000
    )
    .build();

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          AnimChain Demo
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Complex Animation Chain */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">
              Complex Animation Chain
            </h2>
            <p className="text-gray-600 mb-4">
              This recreates the Flutter example with rotating and translating boxes.
            </p>
            
            <div 
              className="relative border-2 border-dashed border-gray-300 bg-gray-50 rounded-lg"
              style={{ height: '300px' }}
            >
              {animationChain}
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p><strong>Sequence:</strong></p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Yellow box appears (5s wait)</li>
                <li>Rotates 180° to red box (3s wait)</li>
                <li>Moves up 150px as blue box (2s wait)</li>
                <li>Repeats...</li>
              </ol>
            </div>
          </div>

          {/* Simple Animation Chain */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">
              Simple Animation Chain
            </h2>
            <p className="text-gray-600 mb-4">
              A basic sequence of different colored boxes.
            </p>
            
            <div className="border-2 border-dashed border-gray-300 bg-gray-50 rounded-lg p-4">
              {simpleChain}
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p><strong>Sequence:</strong></p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Green box (2s)</li>
                <li>Purple box (2s)</li>
                <li>Orange box (2s)</li>
                <li>Repeats...</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Code Examples */}
        <div className="mt-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Code Examples</h2>
          
          <div className="space-y-6">
            {/* Complex Example Code */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Complex Animation Chain</h3>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                <pre className="text-sm">
                  <code>{`const animationChain = createAnimChain()
  .repeat(true)
  .initialDelay(150)
  .next(
    <AnimatedAlignPositioned
      alignment={Alignment.BOTTOM_CENTER}
      rotateDegrees={0}
      duration={0.3}
    >
      <div className="w-16 h-16 bg-yellow-500 rounded-lg">
        Yellow Box
      </div>
    </AnimatedAlignPositioned>,
    5000 // wait 5 seconds
  )
  .next(
    <AnimatedAlignPositioned
      alignment={Alignment.BOTTOM_CENTER}
      rotateDegrees={180}
      duration={3}
    >
      <div className="w-16 h-16 bg-red-500 rounded-lg">
        Red Box
      </div>
    </AnimatedAlignPositioned>,
    3000 // wait 3 seconds
  )
  .next(
    <AnimatedAlignPositioned
      alignment={Alignment.BOTTOM_CENTER}
      dy={-150}
      rotateDegrees={180}
      duration={2}
    >
      <div className="w-16 h-16 bg-blue-500 rounded-lg">
        Blue Box
      </div>
    </AnimatedAlignPositioned>,
    2000 // wait 2 seconds
  )
  .build();`}</code>
                </pre>
              </div>
            </div>

            {/* Simple Example Code */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Simple Animation Chain</h3>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                <pre className="text-sm">
                  <code>{`const simpleChain = createAnimChain()
  .repeat(true)
  .next(
    <div className="bg-green-500 text-white p-4 rounded-lg">
      Step 1: Green Box
    </div>,
    2000
  )
  .next(
    <div className="bg-purple-500 text-white p-4 rounded-lg">
      Step 2: Purple Box
    </div>,
    2000
  )
  .next(
    <div className="bg-orange-500 text-white p-4 rounded-lg">
      Step 3: Orange Box
    </div>,
    2000
  )
  .build();`}</code>
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/"
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            ← Back to AnimatedAlignPositioned Demo
          </a>
        </div>
      </div>
    </div>
  );
}
