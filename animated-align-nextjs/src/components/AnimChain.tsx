'use client';

import React, { ReactNode, useState, useEffect, useRef } from 'react';

interface AnimChainStep {
  widget: ReactNode;
  wait: number; // duration in milliseconds
}

interface AnimChainProps {
  repeat?: boolean;
  initialDelay?: number; // in milliseconds
  steps: AnimChainStep[];
  className?: string;
  style?: React.CSSProperties;
}

const AnimChain: React.FC<AnimChainProps> = ({
  repeat = false,
  initialDelay = 0,
  steps,
  className = '',
  style = {}
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (steps.length === 0) return;

    const startChain = () => {
      if (!mountedRef.current) return;
      
      const currentStep = steps[currentIndex];
      if (!currentStep) return;

      timeoutRef.current = setTimeout(() => {
        if (!mountedRef.current) return;
        
        if (currentIndex < steps.length - 1) {
          setCurrentIndex(prev => prev + 1);
        } else if (repeat) {
          setCurrentIndex(0);
        }
      }, currentStep.wait);
    };

    if (!hasStarted) {
      // Apply initial delay only on first start
      timeoutRef.current = setTimeout(() => {
        if (mountedRef.current) {
          setHasStarted(true);
          startChain();
        }
      }, initialDelay);
    } else {
      startChain();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentIndex, hasStarted, initialDelay, repeat, steps]);

  if (steps.length === 0) {
    return null;
  }

  return (
    <div className={className} style={style}>
      {steps[currentIndex]?.widget}
    </div>
  );
};

// Builder pattern helper class for easier usage
export class AnimChainBuilder {
  private steps: AnimChainStep[] = [];
  private _repeat = false;
  private _initialDelay = 0;

  repeat(repeat: boolean = true): AnimChainBuilder {
    this._repeat = repeat;
    return this;
  }

  initialDelay(delay: number): AnimChainBuilder {
    this._initialDelay = delay;
    return this;
  }

  next(widget: ReactNode, wait: number = 0): AnimChainBuilder {
    this.steps.push({ widget, wait });
    return this;
  }

  build(props?: { className?: string; style?: React.CSSProperties }): ReactNode {
    return (
      <AnimChain
        repeat={this._repeat}
        initialDelay={this._initialDelay}
        steps={this.steps}
        className={props?.className}
        style={props?.style}
      />
    );
  }
}

// Helper function to create a new AnimChain builder
export const createAnimChain = (): AnimChainBuilder => {
  return new AnimChainBuilder();
};

export default AnimChain;
