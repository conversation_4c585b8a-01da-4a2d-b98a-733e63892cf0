'use client';

import React, { ReactNode, useRef, useEffect, useState } from 'react';
import { motion, MotionValue, useMotionValue, animate } from 'framer-motion';

// Enums matching Flutter implementation
export enum Touch {
  INSIDE = 'inside',
  OUTSIDE = 'outside',
  MIDDLE = 'middle'
}

export enum Wins {
  MIN = 'min',
  MAX = 'max'
}

// Alignment enum similar to Flutter
export enum Alignment {
  TOP_LEFT = 'topLeft',
  TOP_CENTER = 'topCenter',
  TOP_RIGHT = 'topRight',
  CENTER_LEFT = 'centerLeft',
  CENTER = 'center',
  CENTER_RIGHT = 'centerRight',
  BOTTOM_LEFT = 'bottomLeft',
  BOTTOM_CENTER = 'bottomCenter',
  BOTTOM_RIGHT = 'bottomRight'
}

interface AnimatedAlignPositionedProps {
  children: ReactNode;

  // Core positioning props
  alignment?: Alignment;
  dx?: number;
  dy?: number;
  
  // Movement by dimensions
  moveByChildWidth?: number;
  moveByChildHeight?: number;
  moveByContainerWidth?: number;
  moveByContainerHeight?: number;
  
  // Orthogonal movement
  moveVerticallyByChildWidth?: number;
  moveHorizontallyByChildHeight?: number;
  moveVerticallyByContainerWidth?: number;
  moveHorizontallyByContainerHeight?: number;
  
  // Size constraints
  childWidth?: number;
  childHeight?: number;
  childWidthRatio?: number;
  childHeightRatio?: number;
  minChildWidth?: number;
  minChildHeight?: number;
  maxChildWidth?: number;
  maxChildHeight?: number;
  minChildWidthRatio?: number;
  minChildHeightRatio?: number;
  maxChildWidthRatio?: number;
  maxChildHeightRatio?: number;
  
  // Transform props
  rotateDegrees?: number;
  
  // Behavior props
  wins?: Wins;
  touch?: Touch;
  
  // Animation props
  duration?: number;
  curve?: string;
  
  // Container props
  className?: string;
  style?: React.CSSProperties;
}

const AnimatedAlignPositioned: React.FC<AnimatedAlignPositionedProps> = ({
  children,
  alignment = Alignment.CENTER,
  dx = 0,
  dy = 0,
  moveByChildWidth = 0,
  moveByChildHeight = 0,
  moveByContainerWidth = 0,
  moveByContainerHeight = 0,
  moveVerticallyByChildWidth = 0,
  moveHorizontallyByChildHeight = 0,
  moveVerticallyByContainerWidth = 0,
  moveHorizontallyByContainerHeight = 0,
  childWidth,
  childHeight,
  childWidthRatio,
  childHeightRatio,
  minChildWidth,
  minChildHeight,
  maxChildWidth,
  maxChildHeight,
  minChildWidthRatio,
  minChildHeightRatio,
  maxChildWidthRatio,
  maxChildHeightRatio,
  rotateDegrees = 0,
  wins = Wins.MIN,
  touch = Touch.INSIDE,
  duration = 0.3,
  curve = 'easeInOut',
  className = '',
  style = {}
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const childRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [childSize, setChildSize] = useState({ width: 0, height: 0 });

  // Motion values for smooth animations
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotate = useMotionValue(rotateDegrees);

  // Helper function to get alignment offset
  const getAlignmentOffset = (containerSize: { width: number; height: number }, childSize: { width: number; height: number }) => {
    const alignmentMap = {
      [Alignment.TOP_LEFT]: { x: 0, y: 0 },
      [Alignment.TOP_CENTER]: { x: (containerSize.width - childSize.width) / 2, y: 0 },
      [Alignment.TOP_RIGHT]: { x: containerSize.width - childSize.width, y: 0 },
      [Alignment.CENTER_LEFT]: { x: 0, y: (containerSize.height - childSize.height) / 2 },
      [Alignment.CENTER]: { x: (containerSize.width - childSize.width) / 2, y: (containerSize.height - childSize.height) / 2 },
      [Alignment.CENTER_RIGHT]: { x: containerSize.width - childSize.width, y: (containerSize.height - childSize.height) / 2 },
      [Alignment.BOTTOM_LEFT]: { x: 0, y: containerSize.height - childSize.height },
      [Alignment.BOTTOM_CENTER]: { x: (containerSize.width - childSize.width) / 2, y: containerSize.height - childSize.height },
      [Alignment.BOTTOM_RIGHT]: { x: containerSize.width - childSize.width, y: containerSize.height - childSize.height }
    };

    return alignmentMap[alignment] || alignmentMap[Alignment.CENTER];
  };

  // Calculate final position
  const calculatePosition = () => {
    if (!containerSize.width || !containerSize.height || !childSize.width || !childSize.height) {
      return { x: 0, y: 0 };
    }

    let finalX = 0;
    let finalY = 0;

    // 1. Apply alignment
    const alignmentOffset = getAlignmentOffset(containerSize, childSize);

    if (touch === Touch.INSIDE) {
      finalX = alignmentOffset.x;
      finalY = alignmentOffset.y;
    } else if (touch === Touch.OUTSIDE) {
      // Position outside the container
      finalX = alignmentOffset.x + (alignmentOffset.x > containerSize.width / 2 ? childSize.width : -childSize.width);
      finalY = alignmentOffset.y + (alignmentOffset.y > containerSize.height / 2 ? childSize.height : -childSize.height);
    } else if (touch === Touch.MIDDLE) {
      // Position at the alignment point, centered
      finalX = alignmentOffset.x - childSize.width / 2;
      finalY = alignmentOffset.y - childSize.height / 2;
    }

    // 2. Add dx and dy (support both absolute pixels and relative values 0-1)
    const dxValue = Math.abs(dx) <= 1 ? dx * containerSize.width : dx;
    const dyValue = Math.abs(dy) <= 1 ? dy * containerSize.height : dy;

    finalX += dxValue;
    finalY += dyValue;

    // 3. Add movement by child dimensions
    finalX += childSize.width * moveByChildWidth + childSize.height * moveHorizontallyByChildHeight;
    finalY += childSize.height * moveByChildHeight + childSize.width * moveVerticallyByChildWidth;

    // 4. Add movement by container dimensions
    finalX += containerSize.width * moveByContainerWidth + containerSize.height * moveHorizontallyByContainerHeight;
    finalY += containerSize.height * moveByContainerHeight + containerSize.width * moveVerticallyByContainerWidth;

    return { x: finalX, y: finalY };
  };

  // Calculate child size constraints
  const calculateChildSize = () => {
    if (!containerSize.width || !containerSize.height) return {};

    const styles: React.CSSProperties = {};

    // Calculate width
    let width = childWidth;
    if (childWidthRatio) {
      width = (width || 0) + containerSize.width * childWidthRatio;
    }

    // Calculate height
    let height = childHeight;
    if (childHeightRatio) {
      height = (height || 0) + containerSize.height * childHeightRatio;
    }

    // Apply min/max constraints
    if (width !== undefined) {
      let minWidth = minChildWidth;
      if (minChildWidthRatio) {
        minWidth = Math.max(minWidth || 0, containerSize.width * minChildWidthRatio);
      }
      
      let maxWidth = maxChildWidth;
      if (maxChildWidthRatio) {
        maxWidth = Math.min(maxWidth || Infinity, containerSize.width * maxChildWidthRatio);
      }

      if (minWidth !== undefined) width = Math.max(width, minWidth);
      if (maxWidth !== undefined) width = Math.min(width, maxWidth);
      
      styles.width = width;
    }

    if (height !== undefined) {
      let minHeight = minChildHeight;
      if (minChildHeightRatio) {
        minHeight = Math.max(minHeight || 0, containerSize.height * minChildHeightRatio);
      }
      
      let maxHeight = maxChildHeight;
      if (maxChildHeightRatio) {
        maxHeight = Math.min(maxHeight || Infinity, containerSize.height * maxChildHeightRatio);
      }

      if (minHeight !== undefined) height = Math.max(height, minHeight);
      if (maxHeight !== undefined) height = Math.min(height, maxHeight);
      
      styles.height = height;
    }

    return styles;
  };

  // Update sizes when container or child changes
  useEffect(() => {
    const updateSizes = () => {
      if (containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        setContainerSize({ width: containerRect.width, height: containerRect.height });
      }
      
      if (childRef.current) {
        const childRect = childRef.current.getBoundingClientRect();
        setChildSize({ width: childRect.width, height: childRect.height });
      }
    };

    updateSizes();
    
    const resizeObserver = new ResizeObserver(updateSizes);
    if (containerRef.current) resizeObserver.observe(containerRef.current);
    if (childRef.current) resizeObserver.observe(childRef.current);

    return () => resizeObserver.disconnect();
  }, [children]);

  // Animate position when dependencies change
  useEffect(() => {
    const position = calculatePosition();

    animate(x, position.x, { duration, ease: curve as any });
    animate(y, position.y, { duration, ease: curve as any });
    animate(rotate, rotateDegrees, { duration, ease: curve as any });
  }, [dx, dy, alignment, containerSize, childSize, moveByChildWidth, moveByChildHeight,
      moveByContainerWidth, moveByContainerHeight, moveVerticallyByChildWidth,
      moveHorizontallyByChildHeight, moveVerticallyByContainerWidth,
      moveHorizontallyByContainerHeight, rotateDegrees, touch, duration, curve]);

  const childStyles = calculateChildSize();

  return (
    <div 
      ref={containerRef}
      className={`relative w-full h-full overflow-hidden ${className}`}
      style={style}
    >
      <motion.div
        ref={childRef}
        style={{
          position: 'absolute',
          x,
          y,
          rotate,
          ...childStyles
        }}
      >
        {children}
      </motion.div>
    </div>
  );
};

export default AnimatedAlignPositioned;
