# AnimatedAlignPositioned for Next.js

A Next.js/React implementation of <PERSON><PERSON><PERSON>'s AnimatedAlignPositioned widget, providing precise positioning and smooth animations for UI elements.

## Features

- **Precise Positioning**: Position elements using `dx` and `dy` offsets with pixel-perfect accuracy
- **Flexible Alignment**: Support for 9 alignment positions (top-left, top-center, top-right, etc.)
- **Touch Modes**: Position elements inside, outside, or at the middle of containers
- **Smooth Animations**: Built with Framer Motion for buttery-smooth transitions
- **Size Constraints**: Control child dimensions with absolute and relative sizing
- **Rotation Support**: Rotate elements with animated transitions
- **Animation Chains**: Create sequences of animated widgets (AnimChain component)

## Installation

This project uses:
- Next.js 15
- React 18
- Framer Motion
- Tailwind CSS
- TypeScript

```bash
npm install framer-motion
```

## Basic Usage

### AnimatedAlignPositioned

```tsx
import AnimatedAlignPositioned, { Alignment } from '@/components/AnimatedAlignPositioned';

function MyComponent() {
  return (
    <div className="relative w-96 h-96 border">
      <AnimatedAlignPositioned
        dx={50}
        dy={30}
        alignment={Alignment.CENTER}
        duration={0.5}
        curve="easeInOut"
      >
        <div className="bg-blue-500 text-white p-4 rounded">
          Positioned Element
        </div>
      </AnimatedAlignPositioned>
    </div>
  );
}
```

### AnimChain

```tsx
import { createAnimChain } from '@/components/AnimChain';
import AnimatedAlignPositioned, { Alignment } from '@/components/AnimatedAlignPositioned';

function AnimationSequence() {
  const animationChain = createAnimChain()
    .repeat(true)
    .initialDelay(150)
    .next(
      <AnimatedAlignPositioned
        alignment={Alignment.BOTTOM_CENTER}
        rotateDegrees={0}
      >
        <div className="w-16 h-16 bg-yellow-500 rounded">Step 1</div>
      </AnimatedAlignPositioned>,
      5000 // wait 5 seconds
    )
    .next(
      <AnimatedAlignPositioned
        alignment={Alignment.BOTTOM_CENTER}
        rotateDegrees={180}
        duration={3}
      >
        <div className="w-16 h-16 bg-red-500 rounded">Step 2</div>
      </AnimatedAlignPositioned>,
      3000 // wait 3 seconds
    )
    .build();

  return (
    <div className="relative w-96 h-96 border">
      {animationChain}
    </div>
  );
}
```

## Props Reference

### AnimatedAlignPositioned Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | The content to be positioned |
| `alignment` | `Alignment` | `CENTER` | Base alignment position |
| `dx` | `number` | `0` | Horizontal offset (pixels if >1, percentage if ≤1) |
| `dy` | `number` | `0` | Vertical offset (pixels if >1, percentage if ≤1) |
| `rotateDegrees` | `number` | `0` | Rotation angle in degrees |
| `duration` | `number` | `0.3` | Animation duration in seconds |
| `curve` | `string` | `'easeInOut'` | Animation easing curve |
| `touch` | `Touch` | `INSIDE` | Positioning mode |
| `childWidth` | `number` | - | Fixed child width |
| `childHeight` | `number` | - | Fixed child height |
| `childWidthRatio` | `number` | - | Child width as ratio of container |
| `childHeightRatio` | `number` | - | Child height as ratio of container |

### Alignment Options

```tsx
enum Alignment {
  TOP_LEFT = 'topLeft',
  TOP_CENTER = 'topCenter',
  TOP_RIGHT = 'topRight',
  CENTER_LEFT = 'centerLeft',
  CENTER = 'center',
  CENTER_RIGHT = 'centerRight',
  BOTTOM_LEFT = 'bottomLeft',
  BOTTOM_CENTER = 'bottomCenter',
  BOTTOM_RIGHT = 'bottomRight'
}
```

### Touch Modes

```tsx
enum Touch {
  INSIDE = 'inside',   // Position inside the container (default)
  OUTSIDE = 'outside', // Position outside the container
  MIDDLE = 'middle'    // Position at the alignment point
}
```

## Advanced Features

### Relative vs Absolute Positioning

The component automatically detects whether to use relative or absolute positioning:

```tsx
// Relative positioning (values between -1 and 1)
<AnimatedAlignPositioned
  dx={0.06577775065104166}  // 6.58% from left edge
  dy={0.07307060128949545}  // 7.31% from top edge
  alignment={Alignment.TOP_LEFT}
>
  <div>Mobile-style relative positioning</div>
</AnimatedAlignPositioned>

// Absolute positioning (values > 1)
<AnimatedAlignPositioned
  dx={50}   // 50 pixels from alignment point
  dy={30}   // 30 pixels from alignment point
>
  <div>Desktop-style absolute positioning</div>
</AnimatedAlignPositioned>
```

### Movement by Dimensions

Position elements relative to their own size or container size:

```tsx
<AnimatedAlignPositioned
  moveByChildWidth={0.5}        // Move right by 50% of child width
  moveByChildHeight={-0.25}     // Move up by 25% of child height
  moveByContainerWidth={0.1}    // Move right by 10% of container width
  moveByContainerHeight={0.1}   // Move down by 10% of container height
>
  <div>Content</div>
</AnimatedAlignPositioned>
```

### Size Constraints

Control child dimensions with min/max constraints:

```tsx
<AnimatedAlignPositioned
  childWidthRatio={0.5}         // 50% of container width
  minChildWidth={100}           // Minimum 100px wide
  maxChildWidth={300}           // Maximum 300px wide
  childHeightRatio={0.3}        // 30% of container height
>
  <div>Sized Content</div>
</AnimatedAlignPositioned>
```

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

Visit the interactive demo to experiment with different positioning options and see the component in action.

## Comparison with Flutter

This Next.js implementation closely mirrors the Flutter AnimatedAlignPositioned widget:

| Feature | Flutter | Next.js | Status |
|---------|---------|---------|--------|
| Basic positioning (dx, dy) | ✅ | ✅ | Complete |
| Alignment options | ✅ | ✅ | Complete |
| Touch modes | ✅ | ✅ | Complete |
| Size constraints | ✅ | ✅ | Complete |
| Movement by dimensions | ✅ | ✅ | Complete |
| Rotation | ✅ | ✅ | Complete |
| Animation curves | ✅ | ✅ | Complete |
| AnimChain sequences | ✅ | ✅ | Complete |

## License

MIT License - feel free to use in your projects!
